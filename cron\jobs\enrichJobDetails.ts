i dont wnt it strict, // cron/jobs/enrichJobDetails.ts

import { logger } from "../utils/logger";
import pLimit from "p-limit";
import { scrapeJobDetailFromUrl } from "lib/search/scrapJobDetailFromUrl";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { matchOrCreateCompany } from "../lib/matchOrCreateCompany";
import { getPrismaClient } from "../utils/prismaClient";

// Import database health reporter
import { DatabaseHealthReporter } from "../utils/dbHealthReporter.js";

// Initialize Prisma client variable
let prisma: any;

// Dynamic configuration based on system resources
const we  = () => {
  const totalMemory = require('os').totalmem();
  return Math.round(totalMemory / 1024 / 1024);
};

const getDynamicBatchSize = () => {
  const memoryMB = getSystemMemoryMB();
  const currentMemoryUsage = process.memoryUsage().rss / 1024 / 1024;
  const availableMemory = memoryMB - currentMemoryUsage;

  // Dynamic batch size based on available memory
  if (availableMemory > 20000) return 200; // 20GB+ available
  if (availableMemory > 10000) return 100; // 10GB+ available
  if (availableMemory > 5000) return 50;   // 5GB+ available
  if (availableMemory > 2000) return 25;   // 2GB+ available
  return 10; // Conservative fallback
};

// Get configuration values or use dynamic defaults
const CONCURRENCY = parseInt(process.env.ENRICH_JOBS_CONCURRENCY ?? "2"); // Increased default concurrency
const BATCH_SIZE = parseInt(process.env.ENRICH_JOBS_BATCH_SIZE ?? getDynamicBatchSize().toString()); // Dynamic batch size
const MAX_MEMORY_THRESHOLD = parseInt(process.env.ENRICH_JOBS_MAX_MEMORY ?? "1500"); // 1.5GB default threshold
const LOCK_TIMEOUT_SECONDS = 3600; // Default lock timeout (1 hour)

// Define startTime at the module level so it's accessible in the catch block
let startTime = Date.now();

// Initialize database health reporter for this job
const healthReporter = new DatabaseHealthReporter("enrichJobDetails");

// Helper function to monitor memory usage
function getMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  return {
    rss: Math.round(memoryUsage.rss / 1024 / 1024), // Resident Set Size in MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // Total heap size in MB
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // Used heap size in MB
    external: Math.round(memoryUsage.external / 1024 / 1024), // External memory in MB
  };
}

export async function enrichJobDetails() {
  // Reset startTime at the beginning of the function
  startTime = Date.now();
  logger.info("🛠️ Enrichment cron started...");

  // Report initial health status
  await healthReporter.reportHealth("operational", {
    uptime: process.uptime(),
    memoryUsage:
      (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100,
  });

  try {
    // Initialize Prisma client
    prisma = await getPrismaClient("web");
    logger.info("✅ Prisma client initialized");

    // Log initial memory usage
    const initialMemory = getMemoryUsage();
    logger.info(
      `🧠 Initial memory usage: RSS: ${initialMemory.rss}MB, Heap: ${initialMemory.heapUsed}MB / ${initialMemory.heapTotal}MB`
    );

    const jobsToEnrich = await prisma.jobListing.findMany({
      where: {
        description: null,
        isActive: true,
      },
      select: {
        id: true,
        url: true,
        company: true,
      },
      take: BATCH_SIZE, // Reduced from 100 to lower memory usage
      orderBy: {
        createdAt: "desc", // Process newest jobs first
      },
    });

    logger.info(`🔎 Found ${jobsToEnrich.length} jobs to enrich`);

    const limit = pLimit(CONCURRENCY);

    // Add memory check function to be called between job processing
    const checkMemoryAndContinue = async (index: number, total: number) => {
      // Check memory usage for every job (changed from every 5 jobs)
      if (index > 0) {
        const currentMemory = getMemoryUsage();
        logger.info(
          `🧠 Memory usage after ${index}/${total} jobs: RSS: ${currentMemory.rss}MB, Heap: ${currentMemory.heapUsed}MB / ${currentMemory.heapTotal}MB`
        );

        // Force garbage collection if available
        if (typeof global.gc === "function") {
          logger.info(`🧹 Running garbage collection...`);
          global.gc();

          // Check memory after GC
          const afterGCMemory = getMemoryUsage();
          logger.info(
            `🧠 Memory after GC: RSS: ${afterGCMemory.rss}MB, Heap: ${afterGCMemory.heapUsed}MB / ${afterGCMemory.heapTotal}MB`
          );
        }

        // If memory usage is too high, abort processing (use dynamic threshold)
        if (currentMemory.rss > MAX_MEMORY_THRESHOLD) {
          logger.warn(
            `⚠️ Memory usage too high (${currentMemory.rss}MB > ${MAX_MEMORY_THRESHOLD}MB), aborting remaining jobs`
          );
          return false;
        }

        // Add a small delay to allow for memory cleanup
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
      return true;
    };

    // Process jobs with memory monitoring
    const tasks = [];
    let jobIndex = 0;

    for (const job of jobsToEnrich) {
      // Check if we should continue based on memory usage
      const shouldContinue = await checkMemoryAndContinue(
        jobIndex,
        jobsToEnrich.length
      );
      if (!shouldContinue) {
        logger.warn(
          `⚠️ Stopping job processing early due to high memory usage after ${jobIndex} jobs`
        );
        break;
      }

      tasks.push(
        limit(async () => {
          try {
            // Log the start of processing for this job
            logger.info(
              `🔄 Processing job ${job.id} (${jobIndex + 1}/${jobsToEnrich.length})`
            );

            const details = await scrapeJobDetailFromUrl(job.url);

            // Try to match or create company with the enriched data
            let companyId;
            if (job.company) {
              companyId = await matchOrCreateCompany(prisma, job.company, {
                applyLink: details.applyLink,
                description: details.description,
              });

              if (companyId) {
                logger.info(
                  `🏢 Matched/created company for job ${job.id}: ${job.company}`
                );
              }
            }

            // Update the job with enriched details
            await prisma.jobListing.update({
              where: { id: job.id },
              data: {
                description: details.description,
                experienceLevel: details.experienceLevel,
                employmentType: details.employmentType,
                remoteType: details.remoteType,
                postedDate: details.postedDate ?? undefined,
                applyLink: details.applyLink,
                requirements: details.requirements,
                benefits: details.benefits,
                skills: details.skills,
                salary: details.salary,
                salaryCurrency: details.salaryCurrency,
                salaryMin: details.salaryMin,
                salaryMax: details.salaryMax,
                securityClearance: details.securityClearance,
                travelRequired: details.travelRequired,
                yearsOfExperience: details.yearsOfExperience,
                experienceRequirements: details.experienceRequirements,
                lastCheckedAt: new Date(),
                // Update company relationship if we found a match
                ...(companyId ? { companyId } : {}),
              },
            });

            logger.info(`✅ Enriched job ${job.id}`);

            // Check memory after each job
            const jobMemory = getMemoryUsage();
            if (jobMemory.rss > 600) {
              // Warning threshold at 600MB (reduced from 1.2GB)
              logger.warn(
                `⚠️ High memory usage after job ${job.id}: RSS: ${jobMemory.rss}MB`
              );
            }
          } catch (err) {
            logger.warn(`⚠️ Failed to enrich job ${job.id}:`, err);
          }
        })
      );

      jobIndex++;
    }

    const results = await Promise.allSettled(tasks);

    // Count successful and failed tasks
    const successCount = results.filter(
      (result) => result.status === "fulfilled"
    ).length;
    const failureCount = results.filter(
      (result) => result.status === "rejected"
    ).length;

    const endTime = Date.now();
    const durationMs = endTime - startTime;
    const durationMinutes = Math.round(durationMs / 1000 / 60);

    // Log final memory usage
    const finalMemory = getMemoryUsage();
    logger.info(
      `🧠 Final memory usage: RSS: ${finalMemory.rss}MB, Heap: ${finalMemory.heapUsed}MB / ${finalMemory.heapTotal}MB`
    );

    // Force garbage collection if available
    let afterGCMemory = finalMemory; // Default to final memory if GC not available
    if (typeof global.gc === "function") {
      logger.info(`🧹 Running final garbage collection...`);
      global.gc();

      // Check memory after GC
      afterGCMemory = getMemoryUsage();
      logger.info(
        `🧠 Memory after final GC: RSS: ${afterGCMemory.rss}MB, Heap: ${afterGCMemory.heapUsed}MB / ${afterGCMemory.heapTotal}MB`
      );
    }

    // Log job statistics in standardized format for easier parsing
    logger.jobStats({
      jobType: "enrichJobDetails",
      processed: jobsToEnrich.length,
      succeeded: successCount,
      failed: failureCount,
      duration: durationMs,
      details: {
        concurrency: CONCURRENCY,
        batchSize: BATCH_SIZE,
        memoryUsage: {
          initial: initialMemory,
          final: finalMemory,
          afterGC: typeof global.gc === "function" ? afterGCMemory : undefined,
        },
        enrichmentTypes: [
          "description",
          "experienceLevel",
          "employmentType",
          "remoteType",
          "applyLink",
          "requirements",
          "benefits",
          "skills",
          "salary",
        ],
        companiesMatched: jobsToEnrich.filter((job: any) => job.company).length,
      },
    });

    logger.info(
      `🎉 Enrichment cron complete. Processed ${jobsToEnrich.length} jobs: ${successCount} succeeded, ${failureCount} failed in ${durationMinutes} minutes.`
    );

    // Report successful completion
    await healthReporter.reportHealth("operational", {
      uptime: process.uptime(),
      memoryUsage:
        (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) *
        100,
      jobsProcessed: jobsToEnrich.length,
      successRate:
        jobsToEnrich.length > 0
          ? (successCount / jobsToEnrich.length) * 100
          : 100,
      duration: durationMs,
    });

    return {
      totalProcessed: jobsToEnrich.length,
      totalSucceeded: successCount,
      totalFailed: failureCount,
      startTime,
      endTime,
      durationMinutes,
    };
  } catch (error) {
    logger.error("❌ Error in job enrichment:", error);

    // Report error status
    await healthReporter.reportHealth("outage", {
      uptime: process.uptime(),
      memoryUsage:
        (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) *
        100,
      error: error instanceof Error ? error.message : String(error),
    });

    throw error;
  } finally {
    // Clean up resources
    try {
      // Disconnect Prisma
      await prisma.$disconnect().catch((err: Error) => {
        logger.error("Error disconnecting Prisma:", err);
      });

      // Force garbage collection if available
      if (typeof global.gc === "function") {
        global.gc();
        logger.info("Forced final garbage collection");
      }
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

/**
 * Main function to run the job with proper cleanup
 */

async function main() {
  try {
    logger.info("🚀 Starting job enrichment");
    const result = await enrichJobDetails();

    // Send email notification with results
    await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
      jobType: "Job Enrichment",
      status: "Completed",
      startTime: new Date(result.startTime).toISOString(),
      endTime: new Date(result.endTime).toISOString(),
      jobsProcessed: result.totalProcessed,
      jobsSucceeded: result.totalSucceeded,
      jobsFailed: result.totalFailed,
      processingTime: `${result.durationMinutes} minutes`,
      reportDate: new Date().toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
    });

    return 0;
  } catch (error: any) {
    const endTime = Date.now();
    const durationMs = endTime - startTime;

    // Log error with standardized job statistics
    logger.error("❌ Error running enrichment job:", error);

    // Log job statistics even for failed jobs
    logger.jobStats({
      jobType: "enrichJobDetails",
      processed: 0,
      succeeded: 0,
      failed: 1, // The job itself failed
      duration: durationMs,
      details: {
        error: error.message ?? String(error),
        errorStack: error.stack,
      },
    });

    // Send error notification
    await sendEmailNotification(EmailNotificationType.CRON_ERROR, {
      jobType: "Job Enrichment",
      status: "Failed",
      errorTitle: "Job Enrichment Failed",
      errorMessage: error.message ?? String(error),
      errorStack: error.stack ?? "No stack trace available",
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      duration: `${Math.round(durationMs / 1000)} seconds`,
    });

    // Report final error status
    await healthReporter.reportHealth("outage", {
      uptime: process.uptime(),
      memoryUsage:
        (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) *
        100,
      error: error.message ?? String(error),
      duration: durationMs,
    });

    return 1;
  }
}

// Run the job with proper cleanup
main()
  .then((exitCode) => {
    process.exit(exitCode);
  })
  .catch((error) => {
    console.error("Unhandled error in main:", error);
    process.exit(1);
  });
