generator client {
  provider        = "prisma-client-js"
  output          = "../node_modules/.prisma/client"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["cron", "web"]
}

model JobListing {
  id                     String           @id @default(cuid())
  platform               String
  jobId                  String
  title                  String
  company                String
  location               String
  url                    String           @unique
  isActive               Boolean          @default(true)
  isProcessing           Boolean          @default(false)
  createdAt              DateTime         @default(now())
  lastCheckedAt          DateTime
  employmentType         String?
  remoteType             String?
  experienceLevel        String?
  description            String?
  postedDate             DateTime?
  closedAt               DateTime?
  applyLink              String?
  benefits               String[]         @default([])
  requirements           String[]         @default([])
  salary                 String?
  salaryCurrency         String?
  salaryMax              Float?
  salaryMin              Float?
  securityClearance      String?
  skills                 String[]         @default([])
  travelRequired         Boolean          @default(false)
  updatedAt              DateTime         @default(now()) @updatedAt
  yearsOfExperience      Int              @default(0)
  experienceRequirements Json?
  companyId              String?
  stateId                String?
  isAnalyzed             Boolean          @default(false)
  companyRelation        Company?         @relation(fields: [companyId], references: [id])
  state                  State?           @relation(fields: [stateId], references: [id])
  jobMatches             JobMatchResult[]

  @@map("job_listing")
  @@schema("cron")
}

model JobMatchResult {
  id         String     @id @default(cuid())
  userId     String
  jobId      String
  profileId  String
  matchScore Float
  applied    Boolean    @default(false)
  createdAt  DateTime   @default(now())
  job        JobListing @relation(fields: [jobId], references: [id])

  @@unique([userId, jobId, profileId])
  @@map("job_match_result")
  @@schema("cron")
}

model JobCollections {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  platform  String
  createdAt DateTime @default(now())

  @@map("job_collections")
  @@schema("cron")
}

model Skills {
  id        String   @id @default(cuid())
  name      String   @unique
  type      String?
  source    String
  createdAt DateTime @default(now())

  @@map("skill")
  @@schema("cron")
}

model Occupations {
  id               String             @id @default(cuid())
  socCode          String
  title            String
  shortTitle       String?
  category         String
  source           String?
  createdAt        DateTime           @default(now())
  jobMarketMetrics JobMarketMetrics[]
  skillTrends      SkillTrend[]

  @@unique([socCode, title])
  @@map("occupation")
  @@schema("cron")
}

model Country {
  id        String    @id @default(cuid())
  name      String    @unique
  isoCode   String?   @unique
  createdAt DateTime  @default(now())
  companies Company[]
  schools   School[]
  states    State[]

  @@map("country")
  @@schema("cron")
}

model State {
  id        String       @id @default(cuid())
  name      String
  code      String?
  countryId String
  createdAt DateTime     @default(now())
  cities    City[]       @relation("StateToCity")
  companies Company[]
  jobs      JobListing[]
  schools   School[]
  country   Country      @relation(fields: [countryId], references: [id], onDelete: Cascade)

  @@unique([countryId, name])
  @@unique([countryId, code], name: "countryId_code")
  @@map("state")
  @@schema("cron")
}

model City {
  id        String   @id @default(cuid())
  name      String
  stateId   String
  createdAt DateTime @default(now())
  state     State    @relation("StateToCity", fields: [stateId], references: [id], onDelete: Cascade)

  @@unique([name, stateId])
  @@index([stateId])
  @@map("city")
  @@schema("cron")
}

model School {
  id          String   @id @default(cuid())
  institution String
  countryId   String?
  stateId     String?
  createdAt   DateTime @default(now())
  country     Country? @relation(fields: [countryId], references: [id])
  state       State?   @relation(fields: [stateId], references: [id])

  @@unique([institution, stateId, countryId])
  @@map("school")
  @@schema("cron")
}

model Language {
  id        String   @id @default(cuid())
  code      String   @unique
  name      String
  createdAt DateTime @default(now())

  @@map("language")
  @@schema("cron")
}

model Company {
  id                    String        @id @default(cuid())
  name                  String        @unique
  domain                String?       @unique
  website               String?
  logoUrl               String?
  overview              String?
  social                Json?
  headquartersCity      String?
  headquartersStateId   String?       @map("headquarters_state_id")
  headquartersCountryId String?       @map("headquarters_country_id")
  companySize           CompanySize?
  companyStage          CompanyStage?
  founded               Int?
  jobCount              Int?
  activeJobCount        Int?
  aiRatingScore         Float?
  aiAnalystNote         Json?
  createdAt             DateTime      @default(now())
  headquartersCountry   Country?      @relation(fields: [headquartersCountryId], references: [id])
  headquartersState     State?        @relation(fields: [headquartersStateId], references: [id])
  jobListings           JobListing[]

  @@map("company")
  @@schema("cron")
}

model scrapeProgress {
  id                  String   @id
  type                String   @unique
  lastCityIndex       Int?
  metadata            String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())
  lastOccupationIndex Int?

  @@index([lastOccupationIndex])
  @@schema("cron")
}

model JobMarketMetrics {
  id           String      @id @default(uuid())
  occupationId String
  level        String?
  remoteCount  Int
  totalCount   Int
  avgSalary    Float?
  salaryRange  Json?
  topSkills    Json?
  topCompanies Json?
  collectedAt  DateTime    @default(now())
  occupation   Occupations @relation(fields: [occupationId], references: [id])

  @@unique([occupationId, level, collectedAt])
  @@schema("cron")
}

model SkillTrend {
  id              String       @id @default(uuid())
  skillName       String
  category        String?
  occupationId    String?
  mentionCount    Int
  growthRate      Float?
  avgSalaryImpact Float?
  collectedAt     DateTime     @default(now())
  occupation      Occupations? @relation(fields: [occupationId], references: [id])

  @@unique([skillName, occupationId, collectedAt])
  @@schema("cron")
}

model JobStats {
  id             String    @id @default(uuid())
  jobType        String
  itemsProcessed Int       @default(0)
  success        Boolean   @default(false)
  durationMs     Int       @default(0)
  details        Json?
  createdAt      DateTime  @default(now())
  startTime      DateTime  @default(now())
  endTime        DateTime?
  error          String?
  updatedAt      DateTime  @updatedAt

  @@index([jobType])
  @@index([createdAt])
  @@index([endTime])
  @@schema("cron")
}

enum CompanySize {
  SIZE_1_10
  SIZE_11_50
  SIZE_51_200
  SIZE_201_500
  SIZE_501_1000
  SIZE_1001_5000
  SIZE_5001_10000
  SIZE_10000_PLUS

  @@schema("cron")
}

enum CompanyStage {
  BOOTSTRAPPED
  PRE_SEED
  SEED
  SERIES_A
  SERIES_B
  SERIES_C
  PUBLIC
  ACQUIRED
  ENTERPRISE

  @@schema("cron")
}
